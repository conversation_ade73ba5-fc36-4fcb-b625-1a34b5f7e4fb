const inputText = document.getElementById('inputText');
const outputText = document.getElementById('outputText');
const encodeButton = document.getElementById('encodeButton');
const decodeButton = document.getElementById('decodeButton');
const copyButton = document.getElementById('copyButton');

const db = new Dexie("index");
db.version(1).stores({
    history: "++id,input,output,createdAt"
});

encodeButton.addEventListener('click', async () => {
    const input = inputText.value;
    if (!input.trim()) {
        outputText.value = '';
        copyButton.classList.remove('show');
        return;
    }
    let encoded = btoa(unescape(encodeURIComponent(input)));
    encoded = encoded.replace(/==$/, '%3D%3D').replace(/=$/, '%3D');
    outputText.value = encoded;

    copyButton.classList.add('show');
    copyButton.classList.remove('copied');
    copyButton.innerHTML = `
        <svg width="10" height="10" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
            <rect x="9" y="9" width="13" height="13" rx="2" ry="2"></rect>
            <path d="M5 15H4a2 2 0 0 1-2-2V4a2 2 0 0 1 2-2h9a2 2 0 0 1 2 2v1"></path>
        </svg>
        复制
    `;

    await db.history.add({
        input,
        output: encoded,
        createdAt: Date.now()
    });
});

decodeButton.addEventListener('click', async () => {
    const input = inputText.value;
    if (!input.trim()) {
        outputText.value = '';
        copyButton.classList.remove('show');
        return;
    }

    try {
        let processedInput = input.replace(/%3D/g, '=');

        let decoded = atob(processedInput);
        decoded = decodeURIComponent(escape(decoded));

        outputText.value = decoded;

        copyButton.classList.add('show');
        copyButton.classList.remove('copied');
        copyButton.innerHTML = `
            <svg width="10" height="10" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                <rect x="9" y="9" width="13" height="13" rx="2" ry="2"></rect>
                <path d="M5 15H4a2 2 0 0 1-2-2V4a2 2 0 0 1 2-2h9a2 2 0 0 1 2 2v1"></path>
            </svg>
            复制
        `;

        await db.history.add({
            input,
            output: decoded,
            createdAt: Date.now()
        });
    } catch (error) {
        outputText.value = '解码失败：输入的不是有效的Base64编码';
        copyButton.classList.remove('show');
    }
});

copyButton.addEventListener('click', async () => {
    const textToCopy = outputText.value;
    if (!textToCopy) return;

    try {
        await navigator.clipboard.writeText(textToCopy);

        copyButton.classList.add('copied');
        copyButton.innerHTML = `
            <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                <polyline points="20,6 9,17 4,12"></polyline>
            </svg>
            已复制
        `;

        setTimeout(() => {
            copyButton.classList.add('fade-out');

            setTimeout(() => {
                copyButton.classList.remove('copied', 'fade-out', 'show');
                copyButton.innerHTML = `
                    <svg width="10" height="10" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                        <rect x="9" y="9" width="13" height="13" rx="2" ry="2"></rect>
                        <path d="M5 15H4a2 2 0 0 1-2-2V4a2 2 0 0 1 2-2h9a2 2 0 0 1 2 2v1"></path>
                    </svg>
                    复制
                `;
            }, 500); 
        }, 2000);

    } catch (err) {

        const textArea = document.createElement('textarea');
        textArea.value = textToCopy;
        textArea.style.position = 'fixed';
        textArea.style.left = '-999999px';
        textArea.style.top = '-999999px';
        document.body.appendChild(textArea);
        textArea.focus();
        textArea.select();

        try {
            document.execCommand('copy');
            copyButton.classList.add('copied');
            copyButton.innerHTML = `
                <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                    <polyline points="20,6 9,17 4,12"></polyline>
                </svg>
                已复制
            `;

            setTimeout(() => {
                copyButton.classList.add('fade-out');

                setTimeout(() => {
                    copyButton.classList.remove('copied', 'fade-out', 'show');
                    copyButton.innerHTML = `
                        <svg width="10" height="10" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                            <rect x="9" y="9" width="13" height="13" rx="2" ry="2"></rect>
                            <path d="M5 15H4a2 2 0 0 1-2-2V4a2 2 0 0 1 2-2h9a2 2 0 0 1 2 2v1"></path>
                        </svg>
                        复制
                    `;
                }, 500); 
            }, 2000);
        } catch (copyErr) {
            console.error('复制失败:', copyErr);
        }

        document.body.removeChild(textArea);
    }
});

// 禁用F12和右键菜单
document.addEventListener('keydown', function (e) {
    if (e.key === 'F12' || (e.ctrlKey && e.shiftKey && e.key === 'I')) {
        e.preventDefault();
        e.stopPropagation();
    }
});
document.addEventListener('contextmenu', function (e) {
    e.preventDefault();
});