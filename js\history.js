const db = new Dexie("index");
db.version(1).stores({
    history: "++id,input,output,createdAt"
});

function getTodayStart() {
    const now = new Date();
    now.setHours(0, 0, 0, 0);
    return now.getTime();
}

async function renderAllHistory() {
    const allHistoryList = document.getElementById('allHistoryList');
    if (!allHistoryList) return;
    const todayStart = getTodayStart();
    const records = await db.history
        .where('createdAt').aboveOrEqual(todayStart)
        .reverse()
        .toArray();

    if (records.length === 0) {
        allHistoryList.innerHTML = '<li style="text-align: center; color: #999; padding: 40px 0;">暂无历史记录</li>';
        return;
    }

    allHistoryList.innerHTML = records.map(item => `
        <li class="history-item">
            <div class="history-row">
                <span class="history-label">输入：</span>
                <span class="history-value">${item.input}</span>
            </div>
            <div class="history-row">
                <span class="history-label">输出：</span>
                <span class="history-value">${item.output}</span>
            </div>
            <div class="history-time">${new Date(item.createdAt).toLocaleString()}</div>
        </li>
    `).join('');
}

renderAllHistory();

document.getElementById('backToTop').addEventListener('click', () => {
    window.scrollTo({
        top: 0,
        behavior: 'smooth'
    });
});

document.getElementById('backToIndex').addEventListener('click', () => {
    window.location.href = '../index.html';
});

function showConfirmModal() {
    const modal = document.getElementById('confirmModal');
    modal.classList.add('show');
    document.body.style.overflow = 'hidden';
}

function hideConfirmModal() {
    const modal = document.getElementById('confirmModal');
    modal.classList.remove('show');
    document.body.style.overflow = '';
}

function showToast(message, type = 'success') {
    const toast = document.createElement('div');
    toast.className = `toast toast-${type}`;
    toast.textContent = message;

    toast.style.cssText = `
        position: fixed;
        top: 20px;
        right: 20px;
        background: ${type === 'success' ? '#10b981' : '#ef4444'};
        color: white;
        padding: 12px 20px;
        border-radius: 8px;
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
        z-index: 10000;
        font-size: 0.9rem;
        font-weight: 500;
        transform: translateX(100%);
        transition: transform 0.3s ease;
    `;

    document.body.appendChild(toast);

    setTimeout(() => {
        toast.style.transform = 'translateX(0)';
    }, 100);

    setTimeout(() => {
        toast.style.transform = 'translateX(100%)';
        setTimeout(() => {
            document.body.removeChild(toast);
        }, 300);
    }, 3000);
}

document.getElementById('clearHistory').addEventListener('click', () => {
    showConfirmModal();
});

document.getElementById('cancelBtn').addEventListener('click', () => {
    hideConfirmModal();
});

document.getElementById('confirmBtn').addEventListener('click', async () => {
    hideConfirmModal();
    try {
        await db.history.clear();
        await renderAllHistory();
        showToast('历史记录已清空！', 'success');
    } catch (error) {
        console.error('清空历史记录失败:', error);
        showToast('清空失败，请重试！', 'error');
    }
});

document.getElementById('confirmModal').addEventListener('click', (e) => {
    if (e.target.id === 'confirmModal') {
        hideConfirmModal();
    }
});

document.getElementById('backToBottom').addEventListener('click', () => {
    window.scrollTo({
        top: document.body.scrollHeight,
        behavior: 'smooth'
    });
});