<!DOCTYPE html>
<html lang="zh">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <title>Base64 转码历史记录</title>
    <link rel="stylesheet" href="../css/styles.css">
      <link rel="icon" href="../img/icon.ico">
    <script src="../js/dexie.min.js"></script>
</head>
<body>
    <!-- 侧边栏 -->
    <div class="sidebar">
        <button class="sidebar-btn" id="backToTop" title="返回顶部">
            <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                <polyline points="18,15 12,9 6,15"></polyline>
            </svg>
        </button>
        <button class="sidebar-btn" id="backToIndex" title="返回首页">
            <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                <path d="M3 9l9-7 9 7v11a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2z"></path>
                <polyline points="9,22 9,12 15,12 15,22"></polyline>
            </svg>
        </button>
        <button class="sidebar-btn sidebar-btn-danger" id="clearHistory" title="清空历史记录">
            <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                <polyline points="3,6 5,6 21,6"></polyline>
                <path d="M19,6v14a2,2 0,0,1-2,2H7a2,2,0,0,1-2-2V6m3,0V4a2,2,0,0,1,2-2h4a2,2,0,0,1,2,2v2"></path>
                <line x1="10" y1="11" x2="10" y2="17"></line>
                <line x1="14" y1="11" x2="14" y2="17"></line>
            </svg>
        </button>
        <button class="sidebar-btn" id="backToBottom" title="返回底部">
            <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                <polyline points="6,9 12,15 18,9"></polyline>
            </svg>
        </button>
    </div>

    <!-- 自定义确认弹窗 -->
    <div class="custom-modal" id="confirmModal">
        <div class="modal-content">
            <div class="modal-header">
                <svg class="modal-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                    <circle cx="12" cy="12" r="10"></circle>
                    <line x1="15" y1="9" x2="9" y2="15"></line>
                    <line x1="9" y1="9" x2="15" y2="15"></line>
                </svg>
                <h3 class="modal-title">确认删除</h3>
            </div>
            <p class="modal-message">确定要清空所有历史记录吗？此操作不可撤销！</p>
            <div class="modal-buttons">
                <button class="modal-btn modal-btn-cancel" id="cancelBtn">取消</button>
                <button class="modal-btn modal-btn-confirm" id="confirmBtn">确认删除</button>
            </div>
        </div>
    </div>

    <div class="main-content">
        <div class="container">
            <h1>Base64 转码历史记录</h1>
            <ul id="allHistoryList"></ul>
        </div>
    </div>
    <div class="footer">
        <div class="footer-content">
            <div class="footer-section">
                <small>本工具仅用于学习与交流，结果请自行验证。</small>
            </div>
            <div class="footer-section">
                <div class="footer-links">
                    <div class="footer-link">隐私政策</div>
                    <span class="separator">|</span>
                    <div class="footer-link">使用条款</div>
                    <span class="separator">|</span>
                    <a href="mailto:<EMAIL>" class="footer-link">联系我们</a>
                </div>
            </div>
            <!-- 底部信息 -->
            <div class="footer-section">
                <div class="copyright">
                    <small>&copy; 2025 Base64转码工具. 保留所有权利.</small>
                </div>
                <div class="beian-info">
                    <small>
                        <a href="https://beian.miit.gov.cn/" target="_blank" rel="noopener" class="beian-link">
                            鲁ICP备2025147997号
                        </a>
                        <span class="separator">|</span>
                        <span class="contact-info">联系邮箱：<EMAIL></span>
                    </small>
                </div>
            </div>
        </div>
    </div>
    <script src="../js/history.js"></script>
</body>
</html>