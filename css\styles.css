body {
    background: linear-gradient(135deg, #f8fafc 0%, #e0e7ff 100%);
    font-family: 'Segoe UI', 'Microsoft YaHei', <PERSON>l, sans-serif;
    margin: 0;
    padding: 0;
    min-height: 100vh;
    display: flex;
    flex-direction: column;
    overflow-x: hidden;
}

.main-content {
    flex: 1;
    display: flex;
    flex-direction: column;
}

h1 {
    text-align: center;
    color: #3730a3;
    margin-bottom: 24px;
    font-size: 2rem;
    letter-spacing: 2px;
}

.container {
    background: #fff;
    max-width: 480px;
    width: 100%;
    margin: 60px auto 0 auto;
    padding: 32px 28px 28px 28px;
    border-radius: 16px;
    box-shadow: 0 4px 24px rgba(60, 72, 88, 0.12);
    display: flex;
    flex-direction: column;
    align-items: stretch;
    box-sizing: border-box;
}

textarea {
    width: 100%;
    min-height: 90px;
    resize: vertical;
    padding: 12px;
    border: 1px solid #c7d2fe;
    border-radius: 8px;
    font-size: 1rem;
    margin-bottom: 18px;
    background: #f1f5f9;
    transition: border-color 0.2s;
    box-sizing: border-box;
}

textarea:focus {
    border-color: #6366f1;
    outline: none;
    background: #fff;
}

.button-group {
    display: flex;
    gap: 12px;
    margin-bottom: 18px;
}

.button-group button {
    flex: 1;
    background: linear-gradient(90deg, #6366f1 0%, #818cf8 100%);
    color: #fff;
    border: none;
    border-radius: 8px;
    padding: 12px 0;
    font-size: 1.1rem;
    font-weight: bold;
    cursor: pointer;
    box-shadow: 0 2px 8px rgba(99, 102, 241, 0.08);
    transition: background 0.2s, transform 0.1s;
    margin-bottom: 0;
}

.button-group button:hover {
    background: linear-gradient(90deg, #4f46e5 0%, #6366f1 100%);
    transform: translateY(-2px) scale(1.03);
}

#decodeButton {
    background: linear-gradient(90deg, #10b981 0%, #34d399 100%);
}

#decodeButton:hover {
    background: linear-gradient(90deg, #059669 0%, #10b981 100%);
}

/* 保持其他按钮的原有样式 */
button:not(.button-group button) {
    background: linear-gradient(90deg, #6366f1 0%, #818cf8 100%);
    color: #fff;
    border: none;
    border-radius: 8px;
    padding: 7px 9px;
    font-size: 1.1rem;
    font-weight: bold;
    cursor: pointer;
    margin-bottom: 21px;
    box-shadow: 0 2px 8px rgba(99, 102, 241, 0.08);
    transition: background 0.2s, transform 0.1s;
}

button:not(.button-group button):hover {
    background: linear-gradient(90deg, #4f46e5 0%, #6366f1 100%);
    transform: translateY(-2px) scale(1.03);
}

.output-container {
    position: relative;
    margin-bottom: 18px;
}

.copy-button {
    position: absolute;
    bottom: 6px;
    right: 6px;
    background: rgba(99, 102, 241, 0.9);
    color: white;
    border: none;
    border-radius: 3px;
    padding: 2px 4px;
    font-size: 0.65rem;
    font-weight: 500;
    cursor: pointer;
    display: flex;
    align-items: center;
    gap: 1px;
    box-shadow: 0 1px 3px rgba(99, 102, 241, 0.2);
    transition: all 0.2s ease;
    margin: 0;
    min-height: auto;
    opacity: 0;
    visibility: hidden;
}

.copy-button:hover {
    background: rgba(79, 70, 229, 0.95);
    transform: translateY(-1px);
    box-shadow: 0 3px 8px rgba(99, 102, 241, 0.3);
}

.copy-button.show {
    opacity: 1;
    visibility: visible;
}

.copy-button.copied {
    background: rgba(34, 197, 94, 0.9);
}

.copy-button.copied:hover {
    background: rgba(34, 197, 94, 0.95);
}

.copy-button.fade-out {
    opacity: 0;
    visibility: hidden;
    transition: opacity 0.5s ease, visibility 0.5s ease;
}

.copy-button svg {
    width: 10px;
    height: 10px;
}

h2 {
    color: #6366f1;
    font-size: 1.1rem;
    margin-bottom: 8px;
    margin-top: 0;
    letter-spacing: 1px;
}

.result {
    margin-top: 20px;
    padding: 10px;
    border: 1px solid #ccc;
    border-radius: 4px;
    background-color: #e9ecef;
    word-wrap: break-word;
}

.view-all-link {
    color: #6366f1;
    text-decoration: none;
    font-size: 0.9rem;
    font-weight: 500;
    transition: color 0.2s ease;
    padding: 4px 8px;
    border-radius: 4px;
    display: inline-block;
}

.view-all-link:hover {
    color: #4f46e5;
    background: rgba(99, 102, 241, 0.05);
}

.footer {
    background: transparent;
    border-top: none;
    margin-top: 40px;
    padding: 20px 20px 16px;
    color: #64748b;
    font-size: 0.875rem;
    line-height: 1.5;
}

.footer-content {
    max-width: 1200px;
    margin: 0 auto;
    text-align: center;
}

.footer-section {
    margin-bottom: 8px;
}

.footer-section:last-child {
    margin-bottom: 0;
}

.footer-links {
    display: flex;
    justify-content: center;
    align-items: center;
    flex-wrap: wrap;
    gap: 8px;
    margin-bottom: 6px;
}

.footer-link {
    color: #64748b;
    text-decoration: none;
    transition: color 0.2s ease;
    padding: 4px 8px;
    border-radius: 4px;
}

.footer-link:hover {
    color: #6366f1;
    background: rgba(99, 102, 241, 0.05);
}

.separator {
    color: #cbd5e1;
    margin: 0 4px;
}

.copyright {
    margin-bottom: 4px;
}

.beian-info {
    display: flex;
    justify-content: center;
    align-items: center;
    flex-wrap: wrap;
    gap: 8px;
}

.beian-link {
    color: #64748b;
    text-decoration: none;
    transition: color 0.2s ease;
    display: inline-flex;
    align-items: center;
    gap: 4px;
    padding: 2px 4px;
    border-radius: 4px;
}

.beian-link:hover {
    color: #6366f1;
    background: rgba(99, 102, 241, 0.05);
}

.beian-icon {
    width: 14px;
    height: 14px;
    opacity: 0.7;
}

.contact-info {
    color: #64748b;
    font-size: inherit;
}

/* 响应式设计 */
@media (max-width: 768px) {
    body {
        padding: 0 16px;
    }

    .container {
        margin: 20px auto 0 auto;
        padding: 24px 20px 20px 20px;
        border-radius: 12px;
        max-width: 100%;
    }

    h1 {
        font-size: 1.5rem;
        margin-bottom: 20px;
        letter-spacing: 1px;
    }

    textarea {
        min-height: 80px;
        padding: 10px;
        font-size: 0.95rem;
        margin-bottom: 16px;
    }

    .button-group {
        gap: 8px;
        margin-bottom: 16px;
    }

    .button-group button {
        padding: 10px 0;
        font-size: 1rem;
    }

    button:not(.button-group button) {
        padding: 10px 0;
        font-size: 1rem;
        margin-bottom: 16px;
    }

    h2 {
        font-size: 1rem;
        margin-bottom: 6px;
    }

    .footer {
        padding: 16px 16px 12px;
        margin-top: 30px;
    }

    .footer-links {
        flex-direction: column;
        gap: 12px;
    }

    .separator {
        display: none;
    }

    .beian-info {
        flex-direction: column;
        gap: 8px;
    }

    .beian-info .separator {
        display: none;
    }
}

@media (max-width: 480px) {
    body {
        padding: 0 12px;
    }

    .container {
        margin: 16px auto 0 auto;
        padding: 20px 16px 16px 16px;
        border-radius: 8px;
    }

    h1 {
        font-size: 1.3rem;
        margin-bottom: 16px;
        letter-spacing: 0.5px;
    }

    textarea {
        min-height: 70px;
        padding: 8px;
        font-size: 0.9rem;
        margin-bottom: 14px;
    }

    .button-group {
        gap: 6px;
        margin-bottom: 14px;
    }

    .button-group button {
        padding: 8px 0;
        font-size: 0.95rem;
    }

    button:not(.button-group button) {
        padding: 8px 0;
        font-size: 0.95rem;
        margin-bottom: 14px;
    }

    .copy-button {
        padding: 2px 3px;
        font-size: 0.6rem;
        bottom: 4px;
        right: 4px;
    }

    .copy-button svg {
        width: 8px;
        height: 8px;
    }

    .footer {
        padding: 14px 12px 10px;
        margin-top: 24px;
        font-size: 0.8rem;
    }

    .view-all-link {
        font-size: 0.85rem;
    }
}

@media (max-width: 360px) {
    body {
        padding: 0 8px;
    }

    .container {
        margin: 12px auto 0 auto;
        padding: 16px 12px 12px 12px;
        border-radius: 6px;
    }

    h1 {
        font-size: 1.2rem;
        margin-bottom: 14px;
        letter-spacing: 0px;
    }

    textarea {
        min-height: 60px;
        padding: 6px;
        font-size: 0.85rem;
        margin-bottom: 12px;
    }

    .button-group {
        gap: 4px;
        margin-bottom: 12px;
    }

    .button-group button {
        padding: 6px 0;
        font-size: 0.9rem;
    }

    button:not(.button-group button) {
        padding: 6px 0;
        font-size: 0.9rem;
        margin-bottom: 12px;
    }

    .footer {
        padding: 12px 8px 8px;
        margin-top: 20px;
        font-size: 0.75rem;
    }

    .footer-content {
        max-width: 100%;
    }

    .footer-section {
        margin-bottom: 6px;
    }

    .footer-links {
        gap: 8px;
    }

    .footer-link {
        padding: 2px 4px;
        font-size: 0.75rem;
    }

    .beian-info {
        gap: 6px;
    }

    .beian-link {
        padding: 1px 2px;
        font-size: 0.7rem;
    }

    .beian-icon {
        width: 12px;
        height: 12px;
    }
}

.history-section {
    max-width: 480px;
    margin: 24px auto 0 auto;
    background: #fff;
    border-radius: 12px;
    box-shadow: 0 2px 12px rgba(60, 72, 88, 0.06);
    padding: 18px 20px;
}

#historyList {
    list-style: none;
    padding: 0;
    margin: 0;
}

#historyList li {
    border-bottom: 1px solid #e5e7eb;
    padding: 10px 0;
    font-size: 0.98rem;
}

#historyList li:last-child {
    border-bottom: none;
}

.history-item {
    padding: 12px 0 8px 0;
    border-bottom: 1px solid #e5e7eb;
}

.history-item:last-child {
    border-bottom: none;
}

.history-row {
    margin-bottom: 4px;
    word-break: break-all;
}

.history-label {
    color: #6366f1;
    font-weight: bold;
}

.history-value {
    color: #22223b;
    user-select: all;
}

.history-time {
    color: #aaa;
    font-size: 0.85em;
    text-align: right;
    margin-top: 2px;
}

/* 侧边栏样式 */
.sidebar {
    position: fixed;
    right: 20px;
    top: 50%;
    transform: translateY(-50%);
    display: flex;
    flex-direction: column;
    gap: 8px;
    z-index: 1000;
}

.sidebar-btn {
    width: 44px;
    height: 44px;
    background: rgba(99, 102, 241, 0.9);
    color: white;
    border: none;
    border-radius: 8px;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: 0 2px 8px rgba(99, 102, 241, 0.3);
    transition: all 0.3s ease;
    backdrop-filter: blur(10px);
}

.sidebar-btn:hover {
    background: rgba(79, 70, 229, 0.95);
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(99, 102, 241, 0.4);
}

.sidebar-btn:active {
    transform: translateY(0);
    box-shadow: 0 2px 6px rgba(99, 102, 241, 0.3);
}

.sidebar-btn svg {
    width: 16px;
    height: 16px;
}

/* 危险按钮样式 */
.sidebar-btn-danger {
    background: rgba(239, 68, 68, 0.9) !important;
}

.sidebar-btn-danger:hover {
    background: rgba(220, 38, 38, 0.95) !important;
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(239, 68, 68, 0.4);
}

/* 自定义弹窗样式 */
.custom-modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 9999;
    opacity: 0;
    visibility: hidden;
    transition: all 0.3s ease;
}

.custom-modal.show {
    opacity: 1;
    visibility: visible;
}

.modal-content {
    background: white;
    border-radius: 12px;
    padding: 24px;
    max-width: 400px;
    width: 90%;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.2);
    transform: scale(0.9);
    transition: transform 0.3s ease;
}

.custom-modal.show .modal-content {
    transform: scale(1);
}

.modal-header {
    display: flex;
    align-items: center;
    margin-bottom: 16px;
}

.modal-icon {
    width: 24px;
    height: 24px;
    margin-right: 12px;
    color: #ef4444;
}

.modal-title {
    font-size: 1.2rem;
    font-weight: bold;
    color: #1f2937;
    margin: 0;
}

.modal-message {
    color: #6b7280;
    margin-bottom: 24px;
    line-height: 1.5;
}

.modal-buttons {
    display: flex;
    gap: 12px;
    justify-content: flex-end;
}

.modal-btn {
    padding: 8px 16px;
    border: none;
    border-radius: 6px;
    font-size: 0.9rem;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;
    min-width: 80px;
}

.modal-btn-cancel {
    background: #f3f4f6;
    color: #374151;
}

.modal-btn-cancel:hover {
    background: #e5e7eb;
}

.modal-btn-confirm {
    background: #ef4444;
    color: white;
}

.modal-btn-confirm:hover {
    background: #dc2626;
}

/* 移动端适配 */
@media (max-width: 768px) {
    .sidebar {
        right: 12px;
        gap: 6px;
    }

    .sidebar-btn {
        width: 38px;
        height: 38px;
        border-radius: 6px;
    }

    .sidebar-btn svg {
        width: 14px;
        height: 14px;
    }

    .modal-content {
        padding: 20px;
        max-width: 320px;
    }

    .modal-title {
        font-size: 1.1rem;
    }

    .modal-message {
        font-size: 0.9rem;
        margin-bottom: 20px;
    }

    .modal-buttons {
        gap: 8px;
    }

    .modal-btn {
        padding: 10px 16px;
        font-size: 0.85rem;
        min-width: 70px;
    }
}