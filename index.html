<!DOCTYPE html>
<html lang="zh">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <script src="./js/dexie.min.js"></script>
    <title>Base64 转码工具</title>
    <link rel="stylesheet" href="./css/styles.css">
    <link rel="icon" href="./img/icon.ico">
<script src="./js/js-sdk-perf.min.js" crossorigin="anonymous"></script>
<script>new LingQue.Monitor().init({id:"3N3AjMUrBAwhONuF",sendSuspicious:true});</script>
</head>
<body>
    <div class="main-content">
        <div class="container">
            <h1>Base64 转码工具</h1>
            <textarea id="inputText" placeholder="请输入文本..."></textarea>
            <div class="button-group">
                <button id="encodeButton">编码</button>
                <button id="decodeButton">解码</button>
            </div>
            <h2>转码结果</h2>
            <div class="output-container">
                <textarea id="outputText" readonly placeholder="转码结果将显示在这里..."></textarea>
                <button id="copyButton" class="copy-button" title="复制转码结果">
                    <svg width="10" height="10" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                        <rect x="9" y="9" width="13" height="13" rx="2" ry="2"></rect>
                        <path d="M5 15H4a2 2 0 0 1-2-2V4a2 2 0 0 1 2-2h9a2 2 0 0 1 2 2v1"></path>
                    </svg>
                    复制
                </button>
            </div>
            <div style="text-align:right;margin-top:8px;">
                <a href="./qlq/history.html" class="view-all-link">查看全部历史记录 &gt;&gt;</a>
            </div>
        </div>
    </div>
    <div class="footer">
        <div class="footer-content">
            <div class="footer-section">
                <small>本工具仅用于学习与交流，结果请自行验证。</small>
            </div>
            <div class="footer-section">
                <div class="footer-links">
                    <div class="footer-link">隐私政策</div>
                    <span class="separator">|</span>
                    <div class="footer-link">使用条款</div>
                    <span class="separator">|</span>
                    <a href="mailto:<EMAIL>" class="footer-link">联系我们</a>
                </div>
            </div>
            <!-- 底部信息 -->
            <div class="footer-section">
                <div class="copyright">
                    <small>&copy; 2025 Base64转码工具. 保留所有权利.</small>
                </div>
                <div class="beian-info">
                    <small>
                        <a href="https://beian.miit.gov.cn/" target="_blank" rel="noopener" class="beian-link">
                            鲁ICP备2025147997号
                        </a>
                        <span class="separator">|</span>
                        <span class="contact-info">联系邮箱：<EMAIL></span>
                    </small>
                </div>
            </div>
        </div>
    </div>
    <script src="./js/app.js"></script>
</body>
</html>